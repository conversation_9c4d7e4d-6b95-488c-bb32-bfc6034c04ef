/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import { OrderingDirection } from '@g17eco/types/common';
import { naturalSort } from '.';
import { SurveyType } from '../types/survey';
import { ScopeQuestionOptionalValue } from '../types/surveyScope';
import { UtrSortingConfig } from '@models/surveyData';


interface SurveyEffectiveDate {
  effectiveDate: string
  created?: string;
  type?: SurveyType;
}

export const sortSurveysDesc = (a: SurveyEffectiveDate, b: SurveyEffectiveDate) => {
  if (a.effectiveDate < b.effectiveDate) {
    return 1;
  }

  if (a.effectiveDate > b.effectiveDate) {
    return -1;
  }

  // Fallback to created
   if (a.created && b.created) {
     if (a.created < b.created) {
       return 1;
     }

     if (a.created > b.created) {
       return -1;
     }
   }

  // Fallback to created
  if (a.type && b.type) {
    if (a.type === 'aggregation' && b.type === 'default') {
      return -1;
    }

    if (a.type === 'default' && b.type === 'aggregation') {
      return 1;
    }
  }

  return 0
}

export const sortEffectiveDateDesc = (a: { effectiveDate: string }, b: { effectiveDate: string }) => {
  if (a.effectiveDate < b.effectiveDate) {
    return 1;
  }

  if (a.effectiveDate > b.effectiveDate) {
    return -1;
  }
  return 0;
}

export const sortEffectiveDateAsc = (a: { effectiveDate: string }, b: { effectiveDate: string }) => {
  if (a.effectiveDate < b.effectiveDate) {
    return -1;
  }

  if (a.effectiveDate > b.effectiveDate) {
    return 1;
  }
  return 0;
};

export interface SortableItem {
  sortTitle?: string;
  unitCount?: number;
}

export interface SortType<T = SortableItem> { field: keyof T, asc: boolean }
export const Sort: { [key: string]: SortType } = {
  TitleAsc: {
    field: 'sortTitle',
    asc: true
  },
  TitleDesc: {
    field: 'sortTitle',
    asc: false
  },
  UnitCountAsc: {
    field: 'unitCount',
    asc: true
  },
  UnitCountDesc: {
    field: 'unitCount',
    asc: false
  }
}

export function sortItems<T = SortableItem>(cards: T[], sortType: SortType<T>) {
  const field = sortType.field;
  const newCards = cards.slice().sort((a, b) => naturalSort(String(a[field]), String(b[field])));

  if (sortType.asc) {
    return newCards;
  } else {
    return newCards.reverse();
  }
}

export function sortItemsByTypeCode<T extends ScopeQuestionOptionalValue = ScopeQuestionOptionalValue>(
  cards: T[],
  alternativeCode?: string
): T[] {
  const getTypeCode = (q: T, alternativeCode?: string) => {
    return q.universalTracker.getTypeCode(alternativeCode) || '';
  };
  return cards.slice().sort((a, b) => (
    naturalSort(getTypeCode(a, alternativeCode), getTypeCode(b, alternativeCode))
    ||
    naturalSort(a.name, b.name))
  );
}

// Iterative function to find the matching subgroup that contains our code
export const findMatchingConfig = (config: UtrSortingConfig | undefined, code: string): UtrSortingConfig | undefined => {
  if (!config) {
    return undefined;
  }

  // Use a stack to traverse the tree iteratively
  const configs: UtrSortingConfig[] = [config];

  while (configs.length > 0) {
    const currentConfig = configs.pop();

    if (currentConfig?.code === code) {
      return currentConfig;
    }

    // Add subgroups to the stack for processing (in reverse order to maintain left-to-right traversal)
    if (currentConfig?.subgroups) {
      for (let i = currentConfig.subgroups.length - 1; i >= 0; i--) {
        configs.push(currentConfig.subgroups[i]);
      }
    }
  }

  return undefined;
};

export const sortItemsByUtrSortingOrder = <T extends ScopeQuestionOptionalValue = ScopeQuestionOptionalValue>(
  questions: T[],
  order: string[],
) => {
  return questions.slice().sort((a, b) => {
    const aCode = a.universalTracker.getCode() || '';
    const bCode = b.universalTracker.getCode() || '';

    // Get indices from the order array
    const aIndex = order.indexOf(aCode);
    const bIndex = order.indexOf(bCode);

    // If both codes are not found in order, maintain original order
    if (aIndex === -1 && bIndex === -1) {
      return 0;
    }

    // If one code is not found, put it at the end
    if (aIndex === -1) {
      return 1;
    }

    if (bIndex === -1) {
      return -1;
    }

    // Sort based on order array indices
    return aIndex - bIndex;
  });
};

type DateAndId = { date: string; _id: string };
export const sortByDateAndId = (a: DateAndId, b: DateAndId) => {
  if (a.date > b.date) return -1;
  if (a.date < b.date) return 1;

  // fallback to _id when date are equal.
  if (a._id > b._id) return -1;
  if (a._id < b._id) return 1;

  return 0;
};

export const DIRECTION_LABEL: Record<OrderingDirection, string> = {
  [OrderingDirection.Asc]: 'Asc',
  [OrderingDirection.Desc]: 'Desc',
  [OrderingDirection.Default]: 'Default',
};