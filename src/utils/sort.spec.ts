import { sortByDateAndId, findMatchingConfig, sortItemsByUtrSortingOrder } from './sort';
import { UtrSortingConfig } from '@models/surveyData';
import { ScopeQuestionOptionalValue } from '@g17eco/types/surveyScope';
import { createScopeQuestion } from '@fixtures/questions-fixture';

describe('utils/sort', () => {
  describe('sortByDateAndId', () => {
    it('should sort desc by date', () => {
      const items = [
        { date: '2023-06-20T10:13:28.132Z', _id: '64917bc80504d3351f9802cc' },
        { date: '2023-06-20T11:13:28.132Z', _id: '64917bc80504d3351f9802cc' },
      ];

      expect(items.sort(sortByDateAndId)).toEqual([
        { date: '2023-06-20T11:13:28.132Z', _id: '64917bc80504d3351f9802cc' },
        { date: '2023-06-20T10:13:28.132Z', _id: '64917bc80504d3351f9802cc' },
      ]);
    });
    it('should fallback to sort desc by _id when date are equal', () => {
      const date = '2023-06-20T10:13:28.132Z';
      const items = [
        { date, _id: '64917bc80504d3351f9802cb' },
        { date, _id: '64917bc80504d3351f9802cc' },
      ];

      expect(items.sort(sortByDateAndId)).toEqual([
        { date, _id: '64917bc80504d3351f9802cc' },
        { date, _id: '64917bc80504d3351f9802cb' },
      ]);
    });
  });

  describe('findMatchingConfig', () => {
    it('should return undefined when config is undefined', () => {
      const result = findMatchingConfig(undefined, 'test-code');
      expect(result).toBeUndefined();
    });

    it('should return the config when code matches directly', () => {
      const config: UtrSortingConfig = {
        code: 'test-code',
        order: ['item1', 'item2'],
      };

      const result = findMatchingConfig(config, 'test-code');
      expect(result).toBe(config);
    });

    it('should find matching config in nested subgroups', () => {
      const deepSubgroup: UtrSortingConfig = {
        code: 'deep-code',
        order: ['deep1', 'deep2'],
      };

      const config: UtrSortingConfig = {
        code: 'root-code',
        order: ['root1', 'root2'],
        subgroups: [
          {
            code: 'level1-code',
            order: ['level1-1', 'level1-2'],
            subgroups: [
              {
                code: 'level2-code',
                order: ['level2-1', 'level2-2'],
                subgroups: [deepSubgroup],
              },
            ],
          },
        ],
      };

      const result = findMatchingConfig(config, 'deep-code');
      expect(result).toBe(deepSubgroup);
    });

    it('should return undefined when code is not found in any subgroup', () => {
      const config: UtrSortingConfig = {
        code: 'root-code',
        order: ['root1', 'root2'],
        subgroups: [
          {
            code: 'sub1-code',
            order: ['sub1-1', 'sub1-2'],
          },
          {
            code: 'sub2-code',
            order: ['sub2-1', 'sub2-2'],
          },
        ],
      };

      const result = findMatchingConfig(config, 'non-existent-code');
      expect(result).toBeUndefined();
    });
  });

  describe('sortItemsByUtrSortingOrder', () => {
    it('should sort questions according to the provided order', () => {
      const questions = [
        createScopeQuestion('code-c', 'Question C'),
        createScopeQuestion('code-a', 'Question A'),
        createScopeQuestion('code-b', 'Question B'),
      ];

      const order = ['code-a', 'code-b', 'code-c'];
      const result = sortItemsByUtrSortingOrder(questions, order);

      expect(result.map((q) => q.universalTracker.getCode())).toEqual(['code-a', 'code-b', 'code-c']);
    });

    it('should maintain original order for items not in the order array', () => {
      const questions = [
        createScopeQuestion('ordered-2', 'Ordered 2'),
        createScopeQuestion('unordered-2', 'Unordered 2'),
        createScopeQuestion('ordered-1', 'Ordered 1'),
        createScopeQuestion('unordered-1', 'Unordered 1'),
      ];

      const order = ['ordered-1', 'ordered-2'];

      const result = sortItemsByUtrSortingOrder(questions, order);

      // Ordered items first, then unordered items maintain their relative order
      expect(result.map((q) => q.universalTracker.getCode())).toEqual([
        'ordered-1',
        'ordered-2',
        'unordered-2',
        'unordered-1',
      ]);
    });

    it('should handle empty order array', () => {
      const questions = [createScopeQuestion('code-a', 'Question A'), createScopeQuestion('code-b', 'Question B')];

      const order: string[] = [];

      const result = sortItemsByUtrSortingOrder(questions, order);

      // Should maintain original order when no order is specified
      expect(result.map((q) => q.universalTracker.getCode())).toEqual(['code-a', 'code-b']);
    });

    it('should handle empty questions array', () => {
      const questions: ScopeQuestionOptionalValue[] = [];
      const order = ['code-a', 'code-b'];

      const result = sortItemsByUtrSortingOrder(questions, order);

      expect(result).toEqual([]);
    });

    it('should handle order array with codes not present in questions', () => {
      const questions = [createScopeQuestion('code-a', 'Question A'), createScopeQuestion('code-b', 'Question B')];

      const order = ['code-x', 'code-a', 'code-y', 'code-b', 'code-z'];

      const result = sortItemsByUtrSortingOrder(questions, order);

      expect(result.map((q) => q.universalTracker.getCode())).toEqual(['code-a', 'code-b']);
    });
  });
});
